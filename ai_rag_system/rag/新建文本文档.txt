rxd@VM-24-2-ubuntu:~$ # 方案1：重启Milvus容器
docker restart milvus-standalone

# 方案2：检查并清理Docker网络
docker network ls
docker network prune

# 方案3：检查系统资源
free -h
df -h

# 方案4：查看Milvus日志
docker logs --tail 50 milvus-standalone
milvus-standalone
NETWORK ID     NAME      DRIVER    SCOPE
4d54f2879797   bridge    bridge    local
8ce90928059f   host      host      local
d287d2cf4a53   none      null      local
WARNING! This will remove all custom networks not used by at least one container.
Are you sure you want to continue? [y/N] y
               total        used        free      shared  buff/cache   available
Mem:           7.4Gi       1.2Gi       1.3Gi       5.5Mi       5.3Gi       6.2Gi
Swap:          1.9Gi       9.1Mi       1.9Gi
Filesystem      Size  Used Avail Use% Mounted on
tmpfs           763M  1.4M  762M   1% /run
/dev/vda2       178G   20G  151G  12% /
tmpfs           3.8G  148K  3.8G   1% /dev/shm
tmpfs           5.0M     0  5.0M   0% /run/lock
overlay         178G   20G  151G  12% /var/lib/docker/overlay2/a53e43aeda025ce6dd3225841fec40cc0fa28700c9b1decf03f2b75ba7d94bbe/merged
overlay         178G   20G  151G  12% /var/lib/docker/overlay2/6062c625878ef7b6520cf81bf5230b3556a52fa080fe83cd88d4ed8eaf0355af/merged
overlay         178G   20G  151G  12% /var/lib/docker/overlay2/877173f04d06a8eae68482f2c736869fbe4a1c70ca19bc49a859f8885bdf41f7/merged
overlay         178G   20G  151G  12% /var/lib/docker/overlay2/48c165504b461e3a796b40923fd047f3dcb2757f556861cee6b725d073c6430d/merged
tmpfs           763M   12K  763M   1% /run/user/999
[2025/08/01 15:39:53.130 +00:00] [INFO] [querycoordv2/server.go:969] ["apply load config changes"] [collectionIDs="[]"] [replicaNum=0] [resourceGroups="[]"]
[2025/08/01 15:39:53.130 +00:00] [INFO] [querycoordv2/services.go:1190] ["update load config request received"] [collectionIDs="[]"] [replicaNumber=0] [resourceGroups="[]"]
[2025/08/01 15:39:53.130 +00:00] [WARN] [querycoordv2/services.go:1193] ["failed to update load config"] [collectionIDs="[]"] [replicaNumber=0] [resourceGroups="[]"] [error="service not ready[standalone=4]: Initializing"]
[2025/08/01 15:39:53.130 +00:00] [INFO] [querycoordv2/server.go:947] ["all old query node down, enable auto balance!"]
[2025/08/01 15:39:53.130 +00:00] [INFO] [proxyutil/proxy_watcher.go:96] ["succeed to init sessions on etcd"] [sessions=null] [revision=146427]
[2025/08/01 15:39:53.131 +00:00] [INFO] [proxyutil/proxy_watcher.go:119] ["start to watch etcd"]
[2025/08/01 15:39:53.133 +00:00] [WARN] [dist/dist_handler.go:125] ["node last heart beat time lag too behind"] [now=2025/08/01 15:39:53.133 +00:00] [lastHeartBeatTime=1970/01/01 00:00:00.000 +00:00] [nodeID=4]
[2025/08/01 15:39:53.133 +00:00] [INFO] [querycoordv2/server.go:589] ["start cluster..."]
[2025/08/01 15:39:53.133 +00:00] [INFO] [querycoordv2/server.go:592] ["start observers..."]
[2025/08/01 15:39:53.133 +00:00] [INFO] [observers/target_observer.go:160] ["Start update next target loop"]
[2025/08/01 15:39:53.133 +00:00] [INFO] [observers/target_observer.go:174] ["target observer init done"]
[2025/08/01 15:39:53.133 +00:00] [INFO] [querycoordv2/server.go:598] ["start task scheduler..."]
[2025/08/01 15:39:53.133 +00:00] [INFO] [querycoordv2/server.go:601] ["start checker controller..."]
[2025/08/01 15:39:53.133 +00:00] [INFO] [querycoordv2/server.go:604] ["start job scheduler..."]
[2025/08/01 15:39:53.133 +00:00] [INFO] [observers/replica_observer.go:72] ["Start check replica loop"]
[2025/08/01 15:39:53.133 +00:00] [INFO] [querycoordv2/server.go:676] ["update querycoord state"] [state=Healthy]
[2025/08/01 15:39:53.133 +00:00] [INFO] [sessionutil/session_util.go:1274] ["save server info into file"] [content="querycoord-4\n"] [filePath=/tmp/milvus/server_id_8]
[2025/08/01 15:39:53.133 +00:00] [INFO] [querycoordv2/server.go:527] ["QueryCoord started"]
[2025/08/01 15:39:53.133 +00:00] [INFO] [observers/resource_observer.go:70] ["Start check resource group loop"]
[2025/08/01 15:39:53.133 +00:00] [INFO] [querycoord/service.go:119] ["QueryCoord start done ..."]
[2025/08/01 15:39:53.133 +00:00] [INFO] [components/query_coord.go:63] ["QueryCoord successfully started"]
[2025/08/01 15:39:53.206 +00:00] [INFO] [sessionutil/session_util.go:954] ["register session success"] [role=querycoord] [key=by-dev/meta/session/querycoord]
[2025/08/01 15:39:53.309 +00:00] [WARN] [grpcclient/client.go:494] ["grpc client is nil, maybe fail to get client in the retry state"] [client_role=querycoord] [error="empty grpc client: find no available querycoord, check querycoord state"] [errorVerbose="empty grpc client: find no available querycoord, check querycoord state\n(1) attached stack trace\n  -- stack trace:\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func2\n  | \t/workspace/source/internal/util/grpcclient/client.go:493\n  | [...repeated from below...]\nWraps: (2) empty grpc client\nWraps: (3) attached stack trace\n  -- stack trace:\n  | github.com/milvus-io/milvus/internal/distributed/querycoord/client.(*Client).getQueryCoordAddr\n  | \t/workspace/source/internal/distributed/querycoord/client/client.go:101\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).connect\n  | \t/workspace/source/internal/util/grpcclient/client.go:260\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).GetGrpcClient\n  | \t/workspace/source/internal/util/grpcclient/client.go:226\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func1\n  | \t/workspace/source/internal/util/grpcclient/client.go:478\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call.func2\n  | \t/workspace/source/internal/util/grpcclient/client.go:495\n  | github.com/milvus-io/milvus/pkg/v2/util/retry.Handle\n  | \t/workspace/source/pkg/util/retry/retry.go:128\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).call\n  | \t/workspace/source/internal/util/grpcclient/client.go:486\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).Call\n  | \t/workspace/source/internal/util/grpcclient/client.go:573\n  | github.com/milvus-io/milvus/internal/util/grpcclient.(*ClientBase[...]).ReCall\n  | \t/workspace/source/internal/util/grpcclient/client.go:589\n  | github.com/milvus-io/milvus/internal/distributed/querycoord/client.wrapGrpcCall[...]\n  | \t/workspace/source/internal/distributed/querycoord/client/client.go:125\n  | github.com/milvus-io/milvus/internal/distributed/querycoord/client.(*Client).GetComponentStates\n  | \t/workspace/source/internal/distributed/querycoord/client/client.go:139\n  | github.com/milvus-io/milvus/internal/util/componentutil.WaitForComponentStates[...].func1\n  | \t/workspace/source/internal/util/componentutil/componentutil.go:39\n  | github.com/milvus-io/milvus/pkg/v2/util/retry.Do\n  | \t/workspace/source/pkg/util/retry/retry.go:44\n  | github.com/milvus-io/milvus/internal/util/componentutil.WaitForComponentStates[...]\n  | \t/workspace/source/internal/util/componentutil/componentutil.go:64\n  | github.com/milvus-io/milvus/internal/util/componentutil.WaitForComponentHealthy[...]\n  | \t/workspace/source/internal/util/componentutil/componentutil.go:85\n  | github.com/milvus-io/milvus/internal/distributed/proxy.(*Server).init\n  | \t/workspace/source/internal/distributed/proxy/service.go:531\n  | github.com/milvus-io/milvus/internal/distributed/proxy.(*Server).Run\n  | \t/workspace/source/internal/distributed/proxy/service.go:413\n  | github.com/milvus-io/milvus/cmd/components.(*Proxy).Run\n  | \t/workspace/source/cmd/components/proxy.go:60\n  | github.com/milvus-io/milvus/cmd/roles.runComponent[...].func1\n  | \t/workspace/source/cmd/roles/roles.go:129\n  | runtime.goexit\n  | \t/go/pkg/mod/golang.org/<EMAIL>-amd64/src/runtime/asm_amd64.s:1700\nWraps: (4) find no available querycoord, check querycoord state\nError types: (1) *withstack.withStack (2) *errutil.withPrefix (3) *withstack.withStack (4) *errutil.leafError"]
[2025/08/01 15:39:53.631 +00:00] [INFO] [dist/dist_handler.go:114] ["pull and handle distribution done"] [respSize=13] [pullDur=1.341275ms] [handleDur=12.834µs]
[2025/08/01 15:39:53.712 +00:00] [INFO] [componentutil/componentutil.go:61] ["WaitForComponentStates success"] ["current state"=Healthy]
[2025/08/01 15:39:53.712 +00:00] [INFO] [proxy/service.go:543] ["register Proxy http server"]
[2025/08/01 15:39:53.726 +00:00] [INFO] [proxy/proxy.go:221] ["init session for Proxy"]
[2025/08/01 15:39:53.727 +00:00] [INFO] [sessionutil/session_util.go:306] ["start server"] [name=proxy] [address=**********:19529] [id=4] [server_labels={}]
[2025/08/01 15:39:53.727 +00:00] [INFO] [sessionutil/session_util.go:1274] ["save server info into file"] [content="proxy-4\n"] [filePath=/tmp/milvus/server_id_8]
[2025/08/01 15:39:53.727 +00:00] [INFO] [proxy/proxy.go:226] ["init session for Proxy done"]
[2025/08/01 15:39:53.727 +00:00] [INFO] [dependency/factory.go:86] ["try to init mq"] [standalone=true] [mqType=rocksmq]
[2025/08/01 15:39:53.727 +00:00] [INFO] [msgstream/mq_factory.go:244] ["init rocksmq msgstream success"] [path=/var/lib/milvus/rdb_data]
[2025/08/01 15:39:53.727 +00:00] [INFO] [proxy/proxy.go:236] ["Proxy init rateCollector done"] [nodeID=4]
[2025/08/01 15:39:53.727 +00:00] [INFO] [msgstream/mq_msgstream.go:125] ["Msg Stream state"] [can_produce=true]
[2025/08/01 15:39:53.727 +00:00] [WARN] [server/rocksmq_impl.go:512] ["rocksmq topic already exists "] [module=rocksmq] [topic=by-dev-replicate-msg]
[2025/08/01 15:39:53.729 +00:00] [INFO] [proxy/meta_cache.go:370] ["success to init meta cache"] [policy_infos="[\"{\\\"PType\\\":\\\"p\\\",\\\"V0\\\":\\\"public\\\",\\\"V1\\\":\\\"Collection-*.*\\\",\\\"V2\\\":\\\"PrivilegeIndexDetail\\\"}\",\"{\\\"PType\\\":\\\"p\\\",\\\"V0\\\":\\\"public\\\",\\\"V1\\\":\\\"Global-*.*\\\",\\\"V2\\\":\\\"PrivilegeDescribeCollection\\\"}\",\"{\\\"PType\\\":\\\"p\\\",\\\"V0\\\":\\\"public\\\",\\\"V1\\\":\\\"Global-*.*\\\",\\\"V2\\\":\\\"PrivilegeListAliases\\\"}\"]"]
[2025/08/01 15:39:53.729 +00:00] [INFO] [proxy/proxy.go:315] ["init proxy done"] [nodeID=4] [Address=**********:19529]
---Milvus Proxy successfully initialized and ready to serve!---
[2025/08/01 15:39:53.729 +00:00] [INFO] [proxy/service.go:417] ["init Proxy server done"]
[2025/08/01 15:39:53.729 +00:00] [INFO] [proxy/service.go:419] ["start Proxy server"]
[2025/08/01 15:39:53.731 +00:00] [INFO] [sessionutil/session_util.go:504] ["put session key into etcd"] [key=by-dev/meta/session/proxy-4] [value="{\"ServerID\":4,\"ServerName\":\"proxy\",\"Address\":\"**********:19529\",\"TriggerKill\":true,\"Version\":\"2.5.15\",\"IndexEngineVersion\":{},\"ScalarIndexEngineVersion\":{},\"LeaseID\":7587888512286938022,\"HostName\":\"c40d53a38b6a\"}"]
[2025/08/01 15:39:53.731 +00:00] [INFO] [sessionutil/session_util.go:514] ["Service registered successfully"] [ServerName=proxy] [serverID=4]
[2025/08/01 15:39:53.731 +00:00] [INFO] [proxy/proxy.go:181] ["Proxy Register Finished"]
[2025/08/01 15:39:53.731 +00:00] [INFO] [rootcoord/timeticksync.go:235] ["Add session for timeticksync"] [serverID=4]
[2025/08/01 15:39:53.731 +00:00] [INFO] [proxy/service.go:577] ["start Proxy http server"]
[2025/08/01 15:39:53.731 +00:00] [INFO] [proxyutil/proxy_client_manager.go:156] ["succeed to create proxy client"] [address=**********:19529] [serverID=4]
[2025/08/01 15:39:53.731 +00:00] [INFO] [proxyutil/proxy_client_manager.go:156] ["succeed to create proxy client"] [address=**********:19529] [serverID=4]
[2025/08/01 15:39:53.731 +00:00] [INFO] [proxy/service.go:424] ["start Proxy server done"]
[2025/08/01 15:39:53.731 +00:00] [INFO] [components/proxy.go:64] ["Proxy successfully started"]
[2025/08/01 15:39:53.809 +00:00] [INFO] [sessionutil/session_util.go:954] ["register session success"] [role=proxy] [key=by-dev/meta/session/proxy-4]
rxd@VM-24-2-ubuntu:~$
