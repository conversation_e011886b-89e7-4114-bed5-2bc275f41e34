root@c40d53a38b6a:/milvus# docker logs --tail 50 milvus-standalone
bash: docker: command not found
root@c40d53a38b6a:/milvus#
exit
rxd@VM-24-2-ubuntu:~$ root@c40d53a38b6a:/milvus# docker logs --tail 50 milvus-standalone
bash: docker: command not found
root@c40d53a38b6a:/milvus#
-bash: root@c40d53a38b6a:/milvus#: No such file or directory
Command 'bash:' not found, did you mean:
  command 'bash' from deb bash (5.2.21-2ubuntu1)
Try: sudo apt install <deb name>
-bash: root@c40d53a38b6a:/milvus#: No such file or directory
rxd@VM-24-2-ubuntu:~$ docker logs --tail 50 milvus-standalone
[2025/08/01 15:15:24.871 +00:00] [INFO] [dist/dist_handler.go:114] ["pull and handle distribution done"] [respSize=13] [pullDur=1.192677ms] [handleDur=13.195µs]
[2025/08/01 15:15:40.870 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476018673] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:16:40.870 +00:00] [INFO] [datacoord/index_meta.go:248] ["update index metric"] [collectionNum=6]
[2025/08/01 15:16:40.870 +00:00] [INFO] [datacoord/import_checker.go:146] ["import job stats"] [stateNum="{\"Failed\":0,\"Completed\":0,\"IndexBuilding\":0,\"Stats\":0,\"Pending\":0,\"PreImporting\":0,\"Importing\":0}"]
[2025/08/01 15:16:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=PreImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:16:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=ImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:16:40.871 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476018974] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:17:25.371 +00:00] [INFO] [dist/dist_handler.go:114] ["pull and handle distribution done"] [respSize=13] [pullDur=1.005244ms] [handleDur=11.391µs]
[2025/08/01 15:17:40.870 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476019275] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:18:40.870 +00:00] [INFO] [datacoord/index_meta.go:248] ["update index metric"] [collectionNum=6]
[2025/08/01 15:18:40.870 +00:00] [INFO] [datacoord/import_checker.go:146] ["import job stats"] [stateNum="{\"IndexBuilding\":0,\"Stats\":0,\"Pending\":0,\"PreImporting\":0,\"Importing\":0,\"Failed\":0,\"Completed\":0}"]
[2025/08/01 15:18:40.870 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476019576] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:18:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=PreImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:18:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=ImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:19:25.372 +00:00] [INFO] [dist/dist_handler.go:114] ["pull and handle distribution done"] [respSize=13] [pullDur=929.51µs] [handleDur=13.275µs]
{"level":"info","ts":"2025-08-01T15:19:38.976Z","caller":"v3compactor/revision.go:86","msg":"starting auto revision compaction","revision":145000,"revision-compaction-retention":1000}
{"level":"info","ts":"2025-08-01T15:19:38.977Z","caller":"v3compactor/revision.go:94","msg":"completed auto revision compaction","revision":145000,"revision-compaction-retention":1000,"took":"1.136721ms"}
{"level":"info","ts":"2025-08-01T15:19:38.977Z","caller":"mvcc/index.go:214","msg":"compact tree index","revision":145000}
{"level":"info","ts":"2025-08-01T15:19:38.977Z","caller":"mvcc/kvstore_compaction.go:66","msg":"finished scheduled compaction","compact-revision":145000,"took":"90.62µs","hash":3589141058}
{"level":"info","ts":"2025-08-01T15:19:38.977Z","caller":"mvcc/hash.go:137","msg":"storing new hash","hash":3589141058,"revision":145000,"compact-revision":144900}
[2025/08/01 15:19:39.358 +00:00] [INFO] [segments/disk_usage_fetcher.go:64] ["querynode disk usage"] [size=0] [nodeID=3]
[2025/08/01 15:19:40.871 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476019877] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:20:40.870 +00:00] [INFO] [datacoord/index_meta.go:248] ["update index metric"] [collectionNum=6]
[2025/08/01 15:20:40.870 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476020178] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:20:40.870 +00:00] [INFO] [datacoord/import_checker.go:146] ["import job stats"] [stateNum="{\"Importing\":0,\"Failed\":0,\"Completed\":0,\"IndexBuilding\":0,\"Stats\":0,\"Pending\":0,\"PreImporting\":0}"]
[2025/08/01 15:20:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=PreImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:20:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=ImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:21:25.871 +00:00] [INFO] [dist/dist_handler.go:114] ["pull and handle distribution done"] [respSize=13] [pullDur=968.343µs] [handleDur=13.136µs]
[2025/08/01 15:21:40.871 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476020479] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:22:40.870 +00:00] [INFO] [datacoord/index_meta.go:248] ["update index metric"] [collectionNum=6]
[2025/08/01 15:22:40.870 +00:00] [INFO] [datacoord/import_checker.go:146] ["import job stats"] [stateNum="{\"IndexBuilding\":0,\"Stats\":0,\"Pending\":0,\"PreImporting\":0,\"Importing\":0,\"Failed\":0,\"Completed\":0}"]
[2025/08/01 15:22:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=PreImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:22:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=ImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:22:40.871 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476020780] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:23:25.871 +00:00] [INFO] [dist/dist_handler.go:114] ["pull and handle distribution done"] [respSize=13] [pullDur=992.149µs] [handleDur=13.816µs]
[2025/08/01 15:23:40.870 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476021081] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:24:38.930 +00:00] [INFO] [server/rocksmq_impl.go:352] ["Rocksmq stats"] [module=rocksmq] [cache=1040] ["rockskv memtable "=2048] ["rockskv table readers"=192] ["rockskv pinned"=96] ["store memtable "=2048] ["store table readers"=0] ["store pinned"=96] ["store l0 file num"=0] ["store l1 file num"=0] ["store l2 file num"=0] ["store l3 file num"=0] ["store l4 file num"=0]
{"level":"info","ts":"2025-08-01T15:24:38.978Z","caller":"v3compactor/revision.go:86","msg":"starting auto revision compaction","revision":145100,"revision-compaction-retention":1000}
{"level":"info","ts":"2025-08-01T15:24:38.979Z","caller":"v3compactor/revision.go:94","msg":"completed auto revision compaction","revision":145100,"revision-compaction-retention":1000,"took":"1.123928ms"}
{"level":"info","ts":"2025-08-01T15:24:38.979Z","caller":"mvcc/index.go:214","msg":"compact tree index","revision":145100}
{"level":"info","ts":"2025-08-01T15:24:38.980Z","caller":"mvcc/kvstore_compaction.go:66","msg":"finished scheduled compaction","compact-revision":145100,"took":"108.695µs","hash":1908682665}
{"level":"info","ts":"2025-08-01T15:24:38.980Z","caller":"mvcc/hash.go:137","msg":"storing new hash","hash":1908682665,"revision":145100,"compact-revision":145000}
[2025/08/01 15:24:39.358 +00:00] [INFO] [segments/disk_usage_fetcher.go:64] ["querynode disk usage"] [size=0] [nodeID=3]
[2025/08/01 15:24:40.870 +00:00] [INFO] [datacoord/index_meta.go:248] ["update index metric"] [collectionNum=6]
[2025/08/01 15:24:40.870 +00:00] [INFO] [datacoord/import_checker.go:146] ["import job stats"] [stateNum="{\"Completed\":0,\"IndexBuilding\":0,\"Stats\":0,\"Pending\":0,\"PreImporting\":0,\"Importing\":0,\"Failed\":0}"]
[2025/08/01 15:24:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=PreImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:24:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=ImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:24:40.871 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476021382] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:24:41.368 +00:00] [INFO] [importv2/util.go:432] ["import task stats"] [type=PreImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:24:41.368 +00:00] [INFO] [importv2/util.go:432] ["import task stats"] [type=ImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
rxd@VM-24-2-ubuntu:~$ docker logs --tail 50 milvus-standalone
[2025/08/01 15:15:40.870 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476018673] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:16:40.870 +00:00] [INFO] [datacoord/index_meta.go:248] ["update index metric"] [collectionNum=6]
[2025/08/01 15:16:40.870 +00:00] [INFO] [datacoord/import_checker.go:146] ["import job stats"] [stateNum="{\"Failed\":0,\"Completed\":0,\"IndexBuilding\":0,\"Stats\":0,\"Pending\":0,\"PreImporting\":0,\"Importing\":0}"]
[2025/08/01 15:16:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=PreImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:16:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=ImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:16:40.871 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476018974] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:17:25.371 +00:00] [INFO] [dist/dist_handler.go:114] ["pull and handle distribution done"] [respSize=13] [pullDur=1.005244ms] [handleDur=11.391µs]
[2025/08/01 15:17:40.870 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476019275] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:18:40.870 +00:00] [INFO] [datacoord/index_meta.go:248] ["update index metric"] [collectionNum=6]
[2025/08/01 15:18:40.870 +00:00] [INFO] [datacoord/import_checker.go:146] ["import job stats"] [stateNum="{\"IndexBuilding\":0,\"Stats\":0,\"Pending\":0,\"PreImporting\":0,\"Importing\":0,\"Failed\":0,\"Completed\":0}"]
[2025/08/01 15:18:40.870 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476019576] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:18:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=PreImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:18:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=ImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:19:25.372 +00:00] [INFO] [dist/dist_handler.go:114] ["pull and handle distribution done"] [respSize=13] [pullDur=929.51µs] [handleDur=13.275µs]
{"level":"info","ts":"2025-08-01T15:19:38.976Z","caller":"v3compactor/revision.go:86","msg":"starting auto revision compaction","revision":145000,"revision-compaction-retention":1000}
{"level":"info","ts":"2025-08-01T15:19:38.977Z","caller":"v3compactor/revision.go:94","msg":"completed auto revision compaction","revision":145000,"revision-compaction-retention":1000,"took":"1.136721ms"}
{"level":"info","ts":"2025-08-01T15:19:38.977Z","caller":"mvcc/index.go:214","msg":"compact tree index","revision":145000}
{"level":"info","ts":"2025-08-01T15:19:38.977Z","caller":"mvcc/kvstore_compaction.go:66","msg":"finished scheduled compaction","compact-revision":145000,"took":"90.62µs","hash":3589141058}
{"level":"info","ts":"2025-08-01T15:19:38.977Z","caller":"mvcc/hash.go:137","msg":"storing new hash","hash":3589141058,"revision":145000,"compact-revision":144900}
[2025/08/01 15:19:39.358 +00:00] [INFO] [segments/disk_usage_fetcher.go:64] ["querynode disk usage"] [size=0] [nodeID=3]
[2025/08/01 15:19:40.871 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476019877] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:20:40.870 +00:00] [INFO] [datacoord/index_meta.go:248] ["update index metric"] [collectionNum=6]
[2025/08/01 15:20:40.870 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476020178] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:20:40.870 +00:00] [INFO] [datacoord/import_checker.go:146] ["import job stats"] [stateNum="{\"Importing\":0,\"Failed\":0,\"Completed\":0,\"IndexBuilding\":0,\"Stats\":0,\"Pending\":0,\"PreImporting\":0}"]
[2025/08/01 15:20:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=PreImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:20:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=ImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:21:25.871 +00:00] [INFO] [dist/dist_handler.go:114] ["pull and handle distribution done"] [respSize=13] [pullDur=968.343µs] [handleDur=13.136µs]
[2025/08/01 15:21:40.871 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476020479] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:22:40.870 +00:00] [INFO] [datacoord/index_meta.go:248] ["update index metric"] [collectionNum=6]
[2025/08/01 15:22:40.870 +00:00] [INFO] [datacoord/import_checker.go:146] ["import job stats"] [stateNum="{\"IndexBuilding\":0,\"Stats\":0,\"Pending\":0,\"PreImporting\":0,\"Importing\":0,\"Failed\":0,\"Completed\":0}"]
[2025/08/01 15:22:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=PreImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:22:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=ImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:22:40.871 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476020780] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:23:25.871 +00:00] [INFO] [dist/dist_handler.go:114] ["pull and handle distribution done"] [respSize=13] [pullDur=992.149µs] [handleDur=13.816µs]
[2025/08/01 15:23:40.870 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476021081] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:24:38.930 +00:00] [INFO] [server/rocksmq_impl.go:352] ["Rocksmq stats"] [module=rocksmq] [cache=1040] ["rockskv memtable "=2048] ["rockskv table readers"=192] ["rockskv pinned"=96] ["store memtable "=2048] ["store table readers"=0] ["store pinned"=96] ["store l0 file num"=0] ["store l1 file num"=0] ["store l2 file num"=0] ["store l3 file num"=0] ["store l4 file num"=0]
{"level":"info","ts":"2025-08-01T15:24:38.978Z","caller":"v3compactor/revision.go:86","msg":"starting auto revision compaction","revision":145100,"revision-compaction-retention":1000}
{"level":"info","ts":"2025-08-01T15:24:38.979Z","caller":"v3compactor/revision.go:94","msg":"completed auto revision compaction","revision":145100,"revision-compaction-retention":1000,"took":"1.123928ms"}
{"level":"info","ts":"2025-08-01T15:24:38.979Z","caller":"mvcc/index.go:214","msg":"compact tree index","revision":145100}
{"level":"info","ts":"2025-08-01T15:24:38.980Z","caller":"mvcc/kvstore_compaction.go:66","msg":"finished scheduled compaction","compact-revision":145100,"took":"108.695µs","hash":1908682665}
{"level":"info","ts":"2025-08-01T15:24:38.980Z","caller":"mvcc/hash.go:137","msg":"storing new hash","hash":1908682665,"revision":145100,"compact-revision":145000}
[2025/08/01 15:24:39.358 +00:00] [INFO] [segments/disk_usage_fetcher.go:64] ["querynode disk usage"] [size=0] [nodeID=3]
[2025/08/01 15:24:40.870 +00:00] [INFO] [datacoord/index_meta.go:248] ["update index metric"] [collectionNum=6]
[2025/08/01 15:24:40.870 +00:00] [INFO] [datacoord/import_checker.go:146] ["import job stats"] [stateNum="{\"Completed\":0,\"IndexBuilding\":0,\"Stats\":0,\"Pending\":0,\"PreImporting\":0,\"Importing\":0,\"Failed\":0}"]
[2025/08/01 15:24:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=PreImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:24:40.871 +00:00] [INFO] [datacoord/import_checker.go:158] ["import task stats"] [type=ImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:24:40.871 +00:00] [INFO] [datacoord/compaction_trigger.go:343] ["the length of candidate group is 0, skip to handle signal"] [compactionID=459811921476021382] [signal.collectionID=0] [signal.partitionID=0] [signal.segmentIDs="[]"]
[2025/08/01 15:24:41.368 +00:00] [INFO] [importv2/util.go:432] ["import task stats"] [type=PreImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:24:41.368 +00:00] [INFO] [importv2/util.go:432] ["import task stats"] [type=ImportTask] [pending=0] [inProgress=0] [completed=0] [failed=0]
[2025/08/01 15:25:25.872 +00:00] [INFO] [dist/dist_handler.go:114] ["pull and handle distribution done"] [respSize=13] [pullDur=1.223295ms] [handleDur=16.23µs]
rxd@VM-24-2-ubuntu:~$ docker exec -it milvus-standalone cat /milvus/configs/milvus.yaml | grep -A 10 -B 10 "proxy\|grpc\|port"
  rootPath: by-dev
  # Sub-prefix of the key to where Milvus stores metadata-related information in etcd.
  # Caution: Changing this parameter after using Milvus for a period of time will affect your access to old data.
  # It is recommended to change this parameter before starting Milvus for the first time.
  metaSubPath: meta
  # Sub-prefix of the key to where Milvus stores timestamps in etcd.
  # Caution: Changing this parameter after using Milvus for a period of time will affect your access to old data.
  # It is recommended not to change this parameter if there is no specific reason.
  kvSubPath: kv
  log:
    level: info # Only supports debug, info, warn, error, panic, or fatal. Default 'info'.
    # path is one of:
    #  - "default" as os.Stderr,
    #  - "stderr" as os.Stderr,
    #  - "stdout" as os.Stdout,
    #  - file path to append server logs to.
    # please adjust in embedded Milvus: /tmp/milvus/logs/etcd.log
    path: stdout
  ssl:
    enabled: false # Whether to support ETCD secure connection mode
    tlsCert: /path/to/etcd-client.pem # path to your cert file
    tlsKey: /path/to/etcd-client-key.pem # path to your key file
    tlsCACert: /path/to/ca.pem # path to your CACert file
    # TLS min version
    # Optional values: 1.0, 1.1, 1.2, 1.3。
    # We recommend using version 1.2 and above.
    tlsMinVersion: 1.3
  requestTimeout: 10000 # Etcd operation timeout in milliseconds
  use:
    embed: false # Whether to enable embedded Etcd (an in-process EtcdServer).
--
  metastore:
  type: etcd # Default value: etcd, Valid values: [etcd, tikv]
  snapshot:
    ttl: 86400 # snapshot ttl in seconds
    reserveTime: 3600 # snapshot reserve time in seconds

# Related configuration of tikv, used to store Milvus metadata.
# Notice that when TiKV is enabled for metastore, you still need to have etcd for service discovery.
# TiKV is a good option when the metadata size requires better horizontal scalability.
tikv:
  endpoints: 127.0.0.1:2389 # Note that the default pd port of tikv is 2379, which conflicts with etcd.
  rootPath: by-dev # The root path where data is stored in tikv
  metaSubPath: meta # metaRootPath = rootPath + '/' + metaSubPath
  kvSubPath: kv # kvRootPath = rootPath + '/' + kvSubPath
  requestTimeout: 10000 # ms, tikv request timeout
  snapshotScanSize: 256 # batch size of tikv snapshot scan
  ssl:
    enabled: false # Whether to support TiKV secure connection mode
    tlsCert:  # path to your cert file
    tlsKey:  # path to your key file
    tlsCACert:  # path to your CACert file

localStorage:
  # Local path to where vector data are stored during a search or a query to avoid repetitve access to MinIO or S3 service.
  # Caution: Changing this parameter after using Milvus for a period of time will affect your access to old data.
  # It is recommended to change this parameter before starting Milvus for the first time.
  path: /var/lib/milvus/data/

# Related configuration of MinIO/S3/GCS or any other service supports S3 API, which is responsible for data persistence for Milvus.
# We refer to the storage service as MinIO/S3 in the following description for simplicity.
minio:
  # IP address of MinIO or S3 service.
  # Environment variable: MINIO_ADDRESS
  # minio.address and minio.port together generate the valid access to MinIO or S3 service.
  # MinIO preferentially acquires the valid IP address from the environment variable MINIO_ADDRESS when Milvus is started.
  # Default value applies when MinIO or S3 is running on the same network with Milvus.
  address: localhost
  port: 9000 # Port of MinIO or S3 service.
  # Access key ID that MinIO or S3 issues to user for authorized access.
  # Environment variable: MINIO_ACCESS_KEY_ID or minio.accessKeyID
  # minio.accessKeyID and minio.secretAccessKey together are used for identity authentication to access the MinIO or S3 service.
  # This configuration must be set identical to the environment variable MINIO_ACCESS_KEY_ID, which is necessary for starting MinIO or S3.
  # The default value applies to MinIO or S3 service that started with the default docker-compose.yml file.
  accessKeyID: minioadmin
  # Secret key used to encrypt the signature string and verify the signature string on server. It must be kept strictly confidential and accessible only to the MinIO or S3 server and users.
  # Environment variable: MINIO_SECRET_ACCESS_KEY or minio.secretAccessKey
  # minio.accessKeyID and minio.secretAccessKey together are used for identity authentication to access the MinIO or S3 service.
  # This configuration must be set identical to the environment variable MINIO_SECRET_ACCESS_KEY, which is necessary for starting MinIO or S3.
  # The default value applies to MinIO or S3 service that started with the default docker-compose.yml file.
  secretAccessKey: minioadmin
  useSSL: false # Switch value to control if to access the MinIO or S3 service through SSL.
  ssl:
    tlsCACert: /path/to/public.crt # path to your CACert file
  # Name of the bucket where Milvus stores data in MinIO or S3.
  # Milvus 2.0.0 does not support storing data in multiple buckets.
  # Bucket with this name will be created if it does not exist. If the bucket already exists and is accessible, it will be used directly. Otherwise, there will be an error.
  # To share an MinIO instance among multiple Milvus instances, consider changing this to a different value for each Milvus instance before you start them. For details, see Operation FAQs.
  # The data will be stored in the local Docker if Docker is used to start the MinIO service locally. Ensure that there is sufficient storage space.
  # A bucket name is globally unique in one MinIO or S3 instance.
  bucketName: a-bucket
  # Root prefix of the key to where Milvus stores data in MinIO or S3.
  # It is recommended to change this parameter before starting Milvus for the first time.
  # To share an MinIO instance among multiple Milvus instances, consider changing this to a different value for each Milvus instance before you start them. For details, see Operation FAQs.
  # Set an easy-to-identify root key prefix for Milvus if etcd service already exists.
  # Changing this for an already running Milvus instance may result in failures to read legacy data.
  rootPath: files
  # Whether to useIAM role to access S3/GCS instead of access/secret keys
  # For more information, refer to
  # aws: https://docs.aws.amazon.com/IAM/latest/UserGuide/id_roles_use.html
  # gcp: https://cloud.google.com/storage/docs/access-control/iam
  # aliyun (ack): https://www.alibabacloud.com/help/en/container-service-for-kubernetes/latest/use-rrsa-to-enforce-access-control
  # aliyun (ecs): https://www.alibabacloud.com/help/en/elastic-compute-service/latest/attach-an-instance-ram-role
  useIAM: false
  # Cloud Provider of S3. Supports: "aws", "gcp", "aliyun".
  # Cloud Provider of Google Cloud Storage. Supports: "gcpnative".
  # You can use "aws" for other cloud provider supports S3 API with signature v4, e.g.: minio
  # You can use "gcp" for other cloud provider supports S3 API with signature v2
  # You can use "aliyun" for other cloud provider uses virtual host style bucket
  # You can use "gcpnative" for the Google Cloud Platform provider. Uses service account credentials
  # for authentication.
  # When useIAM enabled, only "aws", "gcp", "aliyun" is supported for now
  cloudProvider: aws
  # The JSON content contains the gcs service account credentials.
  # Used only for the "gcpnative" cloud provider.
  gcpCredentialJSON:
  # Custom endpoint for fetch IAM role credentials. when useIAM is true & cloudProvider is "aws".
  # Leave it empty if you want to use AWS default endpoint
  iamEndpoint:
  logLevel: fatal # Log level for aws sdk log. Supported level:  off, fatal, error, warn, info, debug, trace
  region:  # Specify minio storage system location region
  useVirtualHost: false # Whether use virtual host mode for bucket
  requestTimeoutMs: 10000 # minio timeout for request time in milliseconds
  # The maximum number of objects requested per batch in minio ListObjects rpc,
  # 0 means using oss client by default, decrease these configration if ListObjects timeout
  listObjectsMaxKeys: 0

# Milvus supports four MQ: rocksmq(based on RockDB), natsmq(embedded nats-server), Pulsar and Kafka.
# You can change your mq by setting mq.type field.
# If you don't set mq.type field as default, there is a note about enabling priority if we config multiple mq in this file.
# 1. standalone(local) mode: rocksmq(default) > natsmq > Pulsar > Kafka
# 2. cluster mode:  Pulsar(default) > Kafka (rocksmq and natsmq is unsupported in cluster mode)
mq:
  # Default value: "default"
  # Valid values: [default, pulsar, kafka, rocksmq, natsmq]
  type: default
  enablePursuitMode: true # Default value: "true"
  pursuitLag: 10 # time tick lag threshold to enter pursuit mode, in seconds
  pursuitBufferSize: 8388608 # pursuit mode buffer size in bytes
  pursuitBufferTime: 60 # pursuit mode buffer time in seconds
  mqBufSize: 16 # MQ client consumer buffer length
  dispatcher:
    mergeCheckInterval: 0.1 # the interval time(in seconds) for dispatcher to check whether to merge
    targetBufSize: 16 # the lenth of channel buffer for targe
    maxTolerantLag: 3 # Default value: "3", the timeout(in seconds) that target sends msgPack

# Related configuration of pulsar, used to manage Milvus logs of recent mutation operations, output streaming log, and provide log publish-subscribe services.
pulsar:
  # IP address of Pulsar service.
  # Environment variable: PULSAR_ADDRESS
  # pulsar.address and pulsar.port together generate the valid access to Pulsar.
  # Pulsar preferentially acquires the valid IP address from the environment variable PULSAR_ADDRESS when Milvus is started.
  # Default value applies when Pulsar is running on the same network with Milvus.
  address: localhost
  port: 6650 # Port of Pulsar service.
  webport: 80 # Web port of of Pulsar service. If you connect direcly without proxy, should use 8080.
  # The maximum size of each message in Pulsar. Unit: Byte.
  # By default, Pulsar can transmit at most 2MB of data in a single message. When the size of inserted data is greater than this value, proxy fragments the data into multiple messages to ensure that they can be transmitted correctly.
  # If the corresponding parameter in Pulsar remains unchanged, increasing this configuration will cause Milvus to fail, and reducing it produces no advantage.
  maxMessageSize: 2097152
  # Pulsar can be provisioned for specific tenants with appropriate capacity allocated to the tenant.
  # To share a Pulsar instance among multiple Milvus instances, you can change this to an Pulsar tenant rather than the default one for each Milvus instance before you start them. However, if you do not want Pulsar multi-tenancy, you are advised to change msgChannel.chanNamePrefix.cluster to the different value.
  tenant: public
  namespace: default # A Pulsar namespace is the administrative unit nomenclature within a tenant.
  requestTimeout: 60 # pulsar client global request timeout in seconds
  enableClientMetrics: false # Whether to register pulsar client metrics into milvus metrics path.

# If you want to enable kafka, needs to comment the pulsar configs
--
    # Prefix of the key to where Milvus stores data in RocksMQ.
  # Caution: Changing this parameter after using Milvus for a period of time will affect your access to old data.
  # It is recommended to change this parameter before starting Milvus for the first time.
  # Set an easy-to-identify root key prefix for Milvus if etcd service already exists.
  path: /var/lib/milvus/rdb_data
  lrucacheratio: 0.06 # rocksdb cache memory ratio
  rocksmqPageSize: 67108864 # The maximum size of messages in each page in RocksMQ. Messages in RocksMQ are checked and cleared (when expired) in batch based on this parameters. Unit: Byte.
  retentionTimeInMinutes: 4320 # The maximum retention time of acked messages in RocksMQ. Acked messages in RocksMQ are retained for the specified period of time and then cleared. Unit: Minute.
  retentionSizeInMB: 8192 # The maximum retention size of acked messages of each topic in RocksMQ. Acked messages in each topic are cleared if their size exceed this parameter. Unit: MB.
  compactionInterval: 86400 # Time interval to trigger rocksdb compaction to remove deleted data. Unit: Second
  compressionTypes: 0,0,7,7,7 # compaction compression type, only support use 0,7. 0 means not compress, 7 will use zstd. Length of types means num of rocksdb level.

# natsmq configuration.
# more detail: https://docs.nats.io/running-a-nats-service/configuration
natsmq:
  server:
    port: 4222 # Listening port of the NATS server.
    storeDir: /var/lib/milvus/nats # Directory to use for JetStream storage of nats
    maxFileStore: 17179869184 # Maximum size of the 'file' storage
    maxPayload: 8388608 # Maximum number of bytes in a message payload
    maxPending: 67108864 # Maximum number of bytes buffered for a connection Applies to client connections
    initializeTimeout: 4000 # waiting for initialization of natsmq finished
    monitor:
      trace: false # If true enable protocol trace log messages
      debug: false # If true enable debug log messages
      logTime: true # If set to false, log without timestamps.
      logFile: /tmp/milvus/logs/nats.log # Log file path relative to .. of milvus binary if use relative path
--
    # Range: [0, INT64MAX]
  maxPartitionNum: 1024
  # The minimum row count of a segment required for creating index.
  # Segments with smaller size than this parameter will not be indexed, and will be searched with brute force.
  minSegmentSizeToEnableIndex: 1024
  enableActiveStandby: false
  maxDatabaseNum: 64 # Maximum number of database
  maxGeneralCapacity: 65536 # upper limit for the sum of of product of partitionNumber and shardNumber
  gracefulStopTimeout: 5 # seconds. force stop node without graceful stop
  ip:  # TCP/IP address of rootCoord. If not specified, use the first unicastable address
  port: 53100 # TCP port of rootCoord
  grpc:
    serverMaxSendSize: 536870912 # The maximum size of each RPC request that the rootCoord can send, unit: byte
    serverMaxRecvSize: 268435456 # The maximum size of each RPC request that the rootCoord can receive, unit: byte
    clientMaxSendSize: 268435456 # The maximum size of each RPC request that the clients on rootCoord can send, unit: byte
    clientMaxRecvSize: 536870912 # The maximum size of each RPC request that the clients on rootCoord can receive, unit: byte

# Related configuration of proxy, used to validate client requests and reduce the returned results.
proxy:
  timeTickInterval: 200 # The interval at which proxy synchronizes the time tick, unit: ms.
  healthCheckTimeout: 3000 # ms, the interval that to do component healthy check
  msgStream:
    timeTick:
      bufSize: 512 # The maximum number of messages can be buffered in the timeTick message stream of the proxy when producing messages.
  maxNameLength: 255 # The maximum length of the name or alias that can be created in Milvus, including the collection name, collection alias, partition name, and field name.
  maxFieldNum: 64 # The maximum number of field can be created when creating in a collection. It is strongly DISCOURAGED to set maxFieldNum >= 64.
  maxVectorFieldNum: 4 # The maximum number of vector fields that can be specified in a collection. Value range: [1, 10].
  maxShardNum: 16 # The maximum number of shards can be created when creating in a collection.
  maxDimension: 32768 # The maximum number of dimensions of a vector can have when creating in a collection.
  # Whether to produce gin logs.\n
  # please adjust in embedded Milvus: false
  ginLogging: true
  ginLogSkipPaths: / # skip url path for gin log
  maxTaskNum: 1024 # The maximum number of tasks in the task queue of the proxy.
  ddlConcurrency: 16 # The concurrent execution number of DDL at proxy.
  dclConcurrency: 16 # The concurrent execution number of DCL at proxy.
  mustUsePartitionKey: false # switch for whether proxy must use partition key for the collection
  accessLog:
    enable: false # Whether to enable the access log feature.
    minioEnable: false # Whether to upload local access log files to MinIO. This parameter can be specified when proxy.accessLog.filename is not empty.
    localPath: /tmp/milvus_access # The local folder path where the access log file is stored. This parameter can be specified when proxy.accessLog.filename is not empty.
    filename:  # The name of the access log file. If you leave this parameter empty, access logs will be printed to stdout.
    maxSize: 64 # The maximum size allowed for a single access log file. If the log file size reaches this limit, a rotation process will be triggered. This process seals the current access log file, creates a new log file, and clears the contents of the original log file. Unit: MB.
    rotatedTime: 0 # The maximum time interval allowed for rotating a single access log file. Upon reaching the specified time interval, a rotation process is triggered, resulting in the creation of a new access log file and sealing of the previous one. Unit: seconds
    remotePath: access_log/ # The path of the object storage for uploading access log files.
    remoteMaxTime: 0 # The time interval allowed for uploading access log files. If the upload time of a log file exceeds this interval, the file will be deleted. Setting the value to 0 disables this feature.
    formatters:
      base:
        format: "[$time_now] [ACCESS] <$user_name: $user_addr> $method_name [status: $method_status] [code: $error_code] [sdk: $sdk_version] [msg: $error_msg] [traceID: $trace_id] [timeCost: $time_cost]"
      query:
        format: "[$time_now] [ACCESS] <$user_name: $user_addr> $method_name [status: $method_status] [code: $error_code] [sdk: $sdk_version] [msg: $error_msg] [traceID: $trace_id] [timeCost: $time_cost] [database: $database_name] [collection: $collection_name] [partitions: $partition_name] [expr: $method_expr] [params: $query_params]"
        methods: "Query, Delete"
      search:
        format: "[$time_now] [ACCESS] <$user_name: $user_addr> $method_name [status: $method_status] [code: $error_code] [sdk: $sdk_version] [msg: $error_msg] [traceID: $trace_id] [timeCost: $time_cost] [database: $database_name] [collection: $collection_name] [partitions: $partition_name] [expr: $method_expr] [nq: $nq] [params: $search_params]"
        methods: "HybridSearch, Search"
    cacheSize: 0 # Size of log of write cache, in byte. (Close write cache if size was 0)
    cacheFlushInterval: 3 # time interval of auto flush write cache, in seconds. (Close auto flush if interval was 0)
  connectionCheckIntervalSeconds: 120 # the interval time(in seconds) for connection manager to scan inactive client info
  connectionClientInfoTTLSeconds: 86400 # inactive client info TTL duration, in seconds
  maxConnectionNum: 10000 # the max client info numbers that proxy should manage, avoid too many client infos
  gracefulStopTimeout: 30 # seconds. force stop node without graceful stop
  slowQuerySpanInSeconds: 5 # query whose executed time exceeds the `slowQuerySpanInSeconds` can be considered slow, in seconds.
  queryNodePooling:
    size: 10 # the size for shardleader(querynode) client pool
  http:
    enabled: true # Whether to enable the http server
    debug_mode: false # Whether to enable http server debug mode
    port:  # high-level restful api
    acceptTypeAllowInt64: true # high-level restful api, whether http client can deal with int64
    enablePprof: true # Whether to enable pprof middleware on the metrics port
    enableWebUI: true # Whether to enable setting the WebUI middleware on the metrics port
  ip:  # TCP/IP address of proxy. If not specified, use the first unicastable address
  port: 19530 # TCP port of proxy
  internalPort: 19529
  grpc:
    serverMaxSendSize: 268435456 # The maximum size of each RPC request that the proxy can send, unit: byte
    serverMaxRecvSize: 67108864 # The maximum size of each RPC request that the proxy can receive, unit: byte
    clientMaxSendSize: 268435456 # The maximum size of each RPC request that the clients on proxy can send, unit: byte
    clientMaxRecvSize: 67108864 # The maximum size of each RPC request that the clients on proxy can receive, unit: byte

# Related configuration of queryCoord, used to manage topology and load balancing for the query nodes, and handoff from growing segments to sealed segments.
queryCoord:
  taskMergeCap: 1
  taskExecutionCap: 256
  # Switch value to control if to automatically replace a growing segment with the corresponding indexed sealed segment when the growing segment reaches the sealing threshold.
  # If this parameter is set false, Milvus simply searches the growing segments with brute force.
  autoHandoff: true
  autoBalance: true # Switch value to control if to automatically balance the memory usage among query nodes by distributing segment loading and releasing operations evenly.
  autoBalanceChannel: true # Enable auto balance channel
--
    checkChannelInterval: 1000
  checkBalanceInterval: 300
  autoBalanceInterval: 3000 # the interval for triggerauto balance
  checkIndexInterval: 10000
  channelTaskTimeout: 60000 # 1 minute
  segmentTaskTimeout: 120000 # 2 minute
  distPullInterval: 500
  heartbeatAvailableInterval: 10000 # 10s, Only QueryNodes which fetched heartbeats within the duration are available
  loadTimeoutSeconds: 600
  distRequestTimeout: 5000 # the request timeout for querycoord fetching data distribution from querynodes, in milliseconds
  heatbeatWarningLag: 5000 # the lag value for querycoord report warning when last heatbeat is too old, in milliseconds
  checkHandoffInterval: 5000
  enableActiveStandby: false
  checkInterval: 1000
  checkHealthInterval: 3000 # 3s, the interval when query coord try to check health of query node
  checkHealthRPCTimeout: 2000 # 100ms, the timeout of check health rpc to query node
  brokerTimeout: 5000 # 5000ms, querycoord broker rpc timeout
  collectionRecoverTimes: 3 # if collection recover times reach the limit during loading state, release it
  observerTaskParallel: 16 # the parallel observer dispatcher task number
  checkAutoBalanceConfigInterval: 10 # the interval of check auto balance config
  checkNodeSessionInterval: 60 # the interval(in seconds) of check querynode cluster session
  gracefulStopTimeout: 5 # seconds. force stop node without graceful stop
  enableStoppingBalance: true # whether enable stopping balance
  channelExclusiveNodeFactor: 4 # the least node number for enable channel's exclusive mode
  collectionObserverInterval: 200 # the interval of collection observer
  checkExecutedFlagInterval: 100 # the interval of check executed flag to force to pull dist
  updateCollectionLoadStatusInterval: 5 # 5m, max interval of updating collection loaded status for check health
  cleanExcludeSegmentInterval: 60 # the time duration of clean pipeline exclude segment which used for filter invalid data, in seconds
  ip:  # TCP/IP address of queryCoord. If not specified, use the first unicastable address
  port: 19531 # TCP port of queryCoord
  grpc:
    serverMaxSendSize: 536870912 # The maximum size of each RPC request that the queryCoord can send, unit: byte
    serverMaxRecvSize: 268435456 # The maximum size of each RPC request that the queryCoord can receive, unit: byte
    clientMaxSendSize: 268435456 # The maximum size of each RPC request that the clients on queryCoord can send, unit: byte
    clientMaxRecvSize: 536870912 # The maximum size of each RPC request that the clients on queryCoord can receive, unit: byte

# Related configuration of queryNode, used to run hybrid search between vector and scalar data.
queryNode:
  stats:
    publishInterval: 1000 # The interval that query node publishes the node statistics information, including segment status, cpu usage, memory usage, health status, etc. Unit: ms.
  segcore:
--
      unsolvedQueueSize: 10240
    # maxReadConcurrentRatio is the concurrency ratio of read task (search task and query task).
    # Max read concurrency would be the value of hardware.GetCPUNum * maxReadConcurrentRatio.
    # It defaults to 2.0, which means max read concurrency would be the value of hardware.GetCPUNum * 2.
    # Max read concurrency must greater than or equal to 1, and less than or equal to hardware.GetCPUNum * 100.
    # (0, 100]
    maxReadConcurrentRatio: 1
    cpuRatio: 10 # ratio used to estimate read task cpu usage.
    maxTimestampLag: 86400
    scheduleReadPolicy:
      # fifo: A FIFO queue support the schedule.
      # user-task-polling:
      #         The user's tasks will be polled one by one and scheduled.
      #         Scheduling is fair on task granularity.
      #         The policy is based on the username for authentication.
      #         And an empty username is considered the same user.
      #         When there are no multi-users, the policy decay into FIFO"
      name: fifo
      taskQueueExpire: 60 # Control how long (many seconds) that queue retains since queue is empty
      enableCrossUserGrouping: false # Enable Cross user grouping when using user-task-polling policy. (Disable it if user's task can not merge each other)
      maxPendingTaskPerUser: 1024 # Max pending task per user in scheduler
--
    enableSegmentPrune: false # use partition stats to prune data in search/query on shard delegator
  queryStreamBatchSize: 4194304 # return min batch size of stream query
  queryStreamMaxBatchSize: 134217728 # return max batch size of stream query
  bloomFilterApplyParallelFactor: 4 # parallel factor when to apply pk to bloom filter, default to 4*CPU_CORE_NUM
  workerPooling:
    size: 10 # the size for worker querynode client pool
  idfOracle:
    enableDisk: true
    writeConcurrency: 4
  ip:  # TCP/IP address of queryNode. If not specified, use the first unicastable address
  port: 21123 # TCP port of queryNode
  grpc:
    serverMaxSendSize: 536870912 # The maximum size of each RPC request that the queryNode can send, unit: byte
    serverMaxRecvSize: 268435456 # The maximum size of each RPC request that the queryNode can receive, unit: byte
    clientMaxSendSize: 268435456 # The maximum size of each RPC request that the clients on queryNode can send, unit: byte
    clientMaxRecvSize: 536870912 # The maximum size of each RPC request that the clients on queryNode can receive, unit: byte

indexCoord:
  bindIndexNodeMode:
    enable: false
    address: localhost:22930
    withCred: false
    nodeID: 0
  segment:
    minSegmentNumRowsToEnableIndex: 1024 # It's a threshold. When the segment num rows is less than this value, the segment will not be indexed

indexNode:
  scheduler:
    buildParallel: 1
  ip:  # TCP/IP address of indexNode. If not specified, use the first unicastable address
  port: 21121 # TCP port of indexNode
  grpc:
    serverMaxSendSize: 536870912 # The maximum size of each RPC request that the indexNode can send, unit: byte
    serverMaxRecvSize: 268435456 # The maximum size of each RPC request that the indexNode can receive, unit: byte
    clientMaxSendSize: 268435456 # The maximum size of each RPC request that the clients on indexNode can send, unit: byte
    clientMaxRecvSize: 536870912 # The maximum size of each RPC request that the clients on indexNode can receive, unit: byte

dataCoord:
  channel:
    watchTimeoutInterval: 300 # Timeout on watching channels (in seconds). Datanode tickler update watch progress will reset timeout timer.
    legacyVersionWithoutRPCWatch: 2.4.1 # Datanodes <= this version are considered as legacy nodes, which doesn't have rpc based watch(). This is only used during rolling upgrade where legacy nodes won't get new channels
    balanceSilentDuration: 300 # The duration after which the channel manager start background channel balancing
    balanceInterval: 360 # The interval with which the channel manager check dml channel balance status
    checkInterval: 1 # The interval in seconds with which the channel manager advances channel states
    notifyChannelOperationTimeout: 5 # Timeout notifing channel operations (in seconds).
  segment:
    maxSize: 1024 # The maximum size of a segment, unit: MB. datacoord.segment.maxSize and datacoord.segment.sealProportion together determine if a segment can be sealed.
    diskSegmentMaxSize: 2048 # Maximun size of a segment in MB for collection which has Disk index
    sealProportion: 0.12 # The minimum proportion to datacoord.segment.maxSize to seal a segment. datacoord.segment.maxSize and datacoord.segment.sealProportion together determine if a segment can be sealed.
    sealProportionJitter: 0.1 # segment seal proportion jitter ratio, default value 0.1(10%), if seal proportion is 12%, with jitter=0.1, the actuall applied ratio will be 10.8~12%
    assignmentExpiration: 2000 # Expiration time of the segment assignment, unit: ms
    allocLatestExpireAttempt: 200 # The time attempting to alloc latest lastExpire from rootCoord after restart
    maxLife: 86400 # The max lifetime of segment in seconds, 24*60*60
    # If a segment didn't accept dml records in maxIdleTime and the size of segment is greater than
    # minSizeFromIdleToSealed, Milvus will automatically seal it.
    # The max idle time of segment in seconds, 10*60.
    maxIdleTime: 600
    minSizeFromIdleToSealed: 16 # The min size in MB of segment which can be idle from sealed.
    # The max number of binlog (which is equal to the binlog file num of primary key) for one segment,
    # the segment will be sealed if the number of binlog file reaches to max value.
    maxBinlogFileNumber: 32
    smallProportion: 0.5 # The segment is considered as "small segment" when its # of rows is smaller than
    # (smallProportion * segment max # of rows).
    # A compaction will happen on small segments if the segment after compaction will have
    compactableProportion: 0.85
    # over (compactableProportion * segment max # of rows) rows.
    # MUST BE GREATER THAN OR EQUAL TO <smallProportion>!!!
    # During compaction, the size of segment # of rows is able to exceed segment max # of rows by (expansionRate-1) * 100%.
    expansionRate: 1.25
  sealPolicy:
    channel:
      # The size threshold in MB, if the total size of growing segments of each shard
      # exceeds this threshold, the largest growing segment will be sealed.
      growingSegmentsMemSize: 4096
      # If the total entry number of l0 logs of each shard
      # exceeds this threshold, the earliest growing segments will be sealed.
      blockingL0EntryNum: 5000000
--
    gc:
    interval: 3600 # The interval at which data coord performs garbage collection, unit: second.
    missingTolerance: 86400 # The retention duration of the unrecorded binary log (binlog) files. Setting a reasonably large value for this parameter avoids erroneously deleting the newly created binlog files that lack metadata. Unit: second.
    dropTolerance: 10800 # The retention duration of the binlog files of the deleted segments before they are cleared, unit: second.
    removeConcurrent: 32 # number of concurrent goroutines to remove dropped s3 objects
    scanInterval: 168 # orphan file (file on oss but has not been registered on meta) on object storage garbage collection scanning interval in hours
  enableActiveStandby: false
  brokerTimeout: 5000 # 5000ms, dataCoord broker rpc timeout
  autoBalance: true # Enable auto balance
  checkAutoBalanceConfigInterval: 10 # the interval of check auto balance config
  import:
    filesPerPreImportTask: 2 # The maximum number of files allowed per pre-import task.
    taskRetention: 10800 # The retention period in seconds for tasks in the Completed or Failed state.
    maxSizeInMBPerImportTask: 6144 # To prevent generating of small segments, we will re-group imported files. This parameter represents the sum of file sizes in each group (each ImportTask).
    scheduleInterval: 2 # The interval for scheduling import, measured in seconds.
    checkIntervalHigh: 2 # The interval for checking import, measured in seconds, is set to a high frequency for the import checker.
    checkIntervalLow: 120 # The interval for checking import, measured in seconds, is set to a low frequency for the import checker.
    maxImportFileNumPerReq: 1024 # The maximum number of files allowed per single import request.
    maxImportJobNum: 1024 # Maximum number of import jobs that are executing or pending.
    waitForIndex: true # Indicates whether the import operation waits for the completion of index building.
  gracefulStopTimeout: 5 # seconds. force stop node without graceful stop
  slot:
    clusteringCompactionUsage: 16 # slot usage of clustering compaction job.
    mixCompactionUsage: 8 # slot usage of mix compaction job.
    l0DeleteCompactionUsage: 8 # slot usage of l0 compaction job.
    indexTaskSlotUsage: 64 # slot usage of index task per 512mb
    statsTaskSlotUsage: 8 # slot usage of stats task per 512mb
    analyzeTaskSlotUsage: 65535 # slot usage of analyze task
  jsonStatsTriggerCount: 10 # jsonkey stats task count per trigger
  jsonStatsTriggerInterval: 10 # jsonkey task interval per trigger
  enabledJSONKeyStatsInSort: false # Indicates whether to enable JSON key stats task with sort
  jsonKeyStatsMemoryBudgetInTantivy: 16777216 # the memory budget for the JSON index In Tantivy, the unit is bytes
  ip:  # TCP/IP address of dataCoord. If not specified, use the first unicastable address
  port: 13333 # TCP port of dataCoord
  grpc:
    serverMaxSendSize: 536870912 # The maximum size of each RPC request that the dataCoord can send, unit: byte
    serverMaxRecvSize: 268435456 # The maximum size of each RPC request that the dataCoord can receive, unit: byte
    clientMaxSendSize: 268435456 # The maximum size of each RPC request that the clients on dataCoord can send, unit: byte
    clientMaxRecvSize: 536870912 # The maximum size of each RPC request that the clients on dataCoord can receive, unit: byte

dataNode:
  dataSync:
    flowGraph:
      maxQueueLength: 16 # Maximum length of task queue in flowgraph
      maxParallelism: 1024 # Maximum number of tasks executed in parallel in the flowgraph
    maxParallelSyncMgrTasks: 256 # The max concurrent sync task number of datanode sync mgr globally
    skipMode:
      enable: true # Support skip some timetick message to reduce CPU usage
      skipNum: 4 # Consume one for every n records skipped
      coldTime: 60 # Turn on skip mode after there are only timetick msg for x seconds
  segment:
    # The maximum size of each binlog file in a segment buffered in memory. Binlog files whose size exceeds this value are then flushed to MinIO or S3 service.
    # Unit: Byte
    # Setting this parameter too small causes the system to store a small amount of data too frequently. Setting it too large increases the system's demand for memory.
    insertBufSize: 16777216
    deleteBufBytes: 16777216 # Max buffer size in bytes to flush del for a single channel, default as 16MB
    syncPeriod: 600 # The period to sync segments if buffer is not empty.
  memory:
--
    # if this parameter <= 0, will set it as the maximum number of CPUs that can be executing
    # suggest to set it bigger on large collection numbers to avoid blocking
    workPoolSize: -1
    # specify the size of global work pool for channel checkpoint updating
    # if this parameter <= 0, will set it as 10
    updateChannelCheckpointMaxParallel: 10
    updateChannelCheckpointInterval: 60 # the interval duration(in seconds) for datanode to update channel checkpoint of each channel
    updateChannelCheckpointRPCTimeout: 20 # timeout in seconds for UpdateChannelCheckpoint RPC call
    maxChannelCheckpointsPerPRC: 128 # The maximum number of channel checkpoints per UpdateChannelCheckpoint RPC.
    channelCheckpointUpdateTickInSeconds: 10 # The frequency, in seconds, at which the channel checkpoint updater executes updates.
  import:
    maxConcurrentTaskNum: 16 # The maximum number of import/pre-import tasks allowed to run concurrently on a datanode.
    maxImportFileSizeInGB: 16 # The maximum file size (in GB) for an import file, where an import file refers to either a Row-Based file or a set of Column-Based files.
    readBufferSizeInMB: 64 # The insert buffer size (in MB) during import.
    readDeleteBufferSizeInMB: 16 # The delete buffer size (in MB) during import.
  compaction:
    levelZeroBatchMemoryRatio: 0.5 # The minimal memory ratio of free memory for level zero compaction executing in batch mode
    levelZeroMaxBatchSize: -1 # Max batch size refers to the max number of L1/L2 segments in a batch when executing L0 compaction. Default to -1, any value that is less than 1 means no limit. Valid range: >= 1.
    useMergeSort: false # Whether to enable mergeSort mode when performing mixCompaction.
    maxSegmentMergeSort: 30 # The maximum number of segments to be merged in mergeSort mode.
  gracefulStopTimeout: 1800 # seconds. force stop node without graceful stop
  slot:
    slotCap: 16 # The maximum number of tasks(e.g. compaction, importing) allowed to run concurrently on a datanode
  clusteringCompaction:
    memoryBufferRatio: 0.3 # The ratio of memory buffer of clustering compaction. Data larger than threshold will be flushed to storage.
    workPoolSize: 8 # worker pool size for one clustering compaction job.
  bloomFilterApplyParallelFactor: 4 # parallel factor when to apply pk to bloom filter, default to 4*CPU_CORE_NUM
  storage:
    deltalog: json # deltalog format, options: [json, parquet]
  ip:  # TCP/IP address of dataNode. If not specified, use the first unicastable address
  port: 21124 # TCP port of dataNode
  grpc:
    serverMaxSendSize: 536870912 # The maximum size of each RPC request that the dataNode can send, unit: byte
    serverMaxRecvSize: 268435456 # The maximum size of each RPC request that the dataNode can receive, unit: byte
    clientMaxSendSize: 268435456 # The maximum size of each RPC request that the clients on dataNode can send, unit: byte
    clientMaxRecvSize: 536870912 # The maximum size of each RPC request that the clients on dataNode can receive, unit: byte

# This topic introduces the message channel-related configurations of Milvus.
msgChannel:
  chanNamePrefix:
    # Root name prefix of the channel when a message channel is created.
    # It is recommended to change this parameter before starting Milvus for the first time.
--
    # The default value is set empty, indicating to output log files to standard output (stdout) and standard error (stderr).
    # If this parameter is set to a valid local path, Milvus writes and stores log files in this path.
    # Set this parameter as the path that you have permission to write.
    rootPath:
    maxSize: 300 # The maximum size of a log file, unit: MB.
    maxAge: 10 # The maximum retention time before a log file is automatically cleared, unit: day. The minimum value is 1.
    maxBackups: 20 # The maximum number of log files to back up, unit: day. The minimum value is 1.
  format: text # Milvus log format. Option: text and JSON
  stdout: true # Stdout enable or not

grpc:
  log:
    level: WARNING
  gracefulStopTimeout: 3 # second, time to wait graceful stop finish
  client:
    compressionEnabled: false
    dialTimeout: 200
    keepAliveTime: 10000
    keepAliveTimeout: 20000
    maxMaxAttempts: 10
    initialBackoff: 0.2
--
        readonly:
          privileges: ShowCollections,DescribeDatabase # Database level readonly privileges
        readwrite:
          privileges: ShowCollections,DescribeDatabase,AlterDatabase # Database level readwrite privileges
        admin:
          privileges: ShowCollections,DescribeDatabase,AlterDatabase,CreateCollection,DropCollection # Database level admin privileges
      collection:
        readonly:
          privileges: Query,Search,IndexDetail,GetFlushState,GetLoadState,GetLoadingProgress,HasPartition,ShowPartitions,DescribeCollection,DescribeAlias,GetStatistics,ListAliases # Collection level readonly privileges
        readwrite:
          privileges: Query,Search,IndexDetail,GetFlushState,GetLoadState,GetLoadingProgress,HasPartition,ShowPartitions,DescribeCollection,DescribeAlias,GetStatistics,ListAliases,Load,Release,Insert,Delete,Upsert,Import,Flush,Compaction,LoadBalance,CreateIndex,DropIndex,CreatePartition,DropPartition # Collection level readwrite privileges
        admin:
          privileges: Query,Search,IndexDetail,GetFlushState,GetLoadState,GetLoadingProgress,HasPartition,ShowPartitions,DescribeCollection,DescribeAlias,GetStatistics,ListAliases,Load,Release,Insert,Delete,Upsert,Import,Flush,Compaction,LoadBalance,CreateIndex,DropIndex,CreatePartition,DropPartition,CreateAlias,DropAlias # Collection level admin privileges
    internaltlsEnabled: false
    tlsMode: 0
  session:
    ttl: 30 # ttl value when session granting a lease to register service
    retryTimes: 30 # retry times when session sending etcd requests
  locks:
    metrics:
      enable: false # whether gather statistics for metrics locks
    threshold:
      info: 500 # minimum milliseconds for printing durations in info level
--
    maxWLockConditionalWaitTime: 600 # maximum seconds for waiting wlock conditional
  storage:
    scheme: s3
    enablev2: false
  # Whether to disable the internal time messaging mechanism for the system.
  # If disabled (set to false), the system will not allow DML operations, including insertion, deletion, queries, and searches.
  # This helps Milvus-CDC synchronize incremental data
  ttMsgEnabled: true
  traceLogMode: 0 # trace request info
  bloomFilterSize: 100000 # bloom filter initial size
  bloomFilterType: BlockedBloomFilter # bloom filter type, support BasicBloomFilter and BlockedBloomFilter
  maxBloomFalsePositive: 0.001 # max false positive rate for bloom filter
  bloomFilterApplyBatchSize: 1000 # batch size when to apply pk to bloom filter
  collectionReplicateEnable: false # Whether to enable collection replication.
  usePartitionKeyAsClusteringKey: false # if true, do clustering compaction and segment prune on partition key field
  useVectorAsClusteringKey: false # if true, do clustering compaction and segment prune on vector field
  enableVectorClusteringKey: false # if true, enable vector clustering key and vector clustering compaction
  localRPCEnabled: false # enable local rpc for internal communication when mix or standalone mode.
  sync:
    taskPoolReleaseTimeoutSeconds: 60 # The maximum time to wait for the task to finish and release resources in the pool
  enabledOptimizeExpr: true # Indicates whether to enable optimize expr
--
      db:
        max: -1 # MB/s, default no limit
      collection:
        # Highest data deletion rate per second.
        # Setting this item to 0.1 indicates that Milvus only allows data deletion from any collection at the rate of 0.1 MB/s.
        # To use this setting, set quotaAndLimits.dml.enabled to true at the same time.
        max: -1
      partition:
        max: -1 # MB/s, default no limit
    bulkLoadRate:
      max: -1 # MB/s, default no limit, not support yet. TODO: limit bulkLoad rate
      db:
        max: -1 # MB/s, default no limit, not support yet. TODO: limit db bulkLoad rate
      collection:
        max: -1 # MB/s, default no limit, not support yet. TODO: limit collection bulkLoad rate
      partition:
        max: -1 # MB/s, default no limit, not support yet. TODO: limit partition bulkLoad rate
  dql:
    enabled: false # Whether DQL request throttling is enabled.
    searchRate:
      # Maximum number of vectors to search per second.
      # Setting this item to 100 indicates that Milvus only allows searching 100 vectors per second no matter whether these 100 vectors are all in one search or scattered across multiple searches.
      # To use this setting, set quotaAndLimits.dql.enabled to true at the same time.
      max: -1
      db:
        max: -1 # vps (vectors per second), default no limit
      collection:
--
    deleteBufferSizeProtection:
      enabled: false # switch to enable delete buffer size quota
      lowWaterLevel: 134217728 # delete buffer size quota, low water level
      highWaterLevel: 268435456 # delete buffer size quota, high water level
  limitReading:
    # forceDeny false means dql requests are allowed (except for some
    # specific conditions, such as collection has been dropped), true means always reject all dql requests.
    forceDeny: false

trace:
  # trace exporter type, default is stdout,
  # optional values: ['noop','stdout', 'jaeger', 'otlp']
  exporter: noop
  # fraction of traceID based sampler,
  # optional values: [0, 1]
  # Fractions >= 1 will always sample. Fractions < 0 are treated as zero.
  sampleFraction: 0
  jaeger:
    url:  # when exporter is jaeger should set the jaeger's URL
  otlp:
    endpoint:  # example: "127.0.0.1:4317" for grpc, "127.0.0.1:4318" for http
    method:  # otlp export method, acceptable values: ["grpc", "http"],  using "grpc" by default
    secure: true
  initTimeoutSeconds: 10 # segcore initialization timeout in seconds, preventing otlp grpc hangs forever

#when using GPU indexing, Milvus will utilize a memory pool to avoid frequent memory allocation and deallocation.
#here, you can set the size of the memory occupied by the memory pool, with the unit being MB.
#note that there is a possibility of Milvus crashing when the actual memory demand exceeds the value set by maxMemSize.
#if initMemSize and MaxMemSize both set zero,
#milvus will automatically initialize half of the available GPU memory,
#maxMemSize will the whole available GPU memory.
gpu:
  initMemSize: 2048 # Gpu Memory Pool init size
  maxMemSize: 4096 # Gpu Memory Pool Max size

# Any configuration related to the streaming node server.
streamingNode:
  ip:  # TCP/IP address of streamingNode. If not specified, use the first unicastable address
  port: 22222 # TCP port of streamingNode
  grpc:
    serverMaxSendSize: 268435456 # The maximum size of each RPC request that the streamingNode can send, unit: byte
    serverMaxRecvSize: 268435456 # The maximum size of each RPC request that the streamingNode can receive, unit: byte
    clientMaxSendSize: 268435456 # The maximum size of each RPC request that the clients on streamingNode can send, unit: byte
    clientMaxRecvSize: 268435456 # The maximum size of each RPC request that the clients on streamingNode can receive, unit: byte

# Any configuration related to the streaming service.
streaming:
  walBalancer:
    # The interval of balance task trigger at background, 1 min by default.
    # It's ok to set it into duration string, such as 30s or 1m30s, see time.ParseDuration
rxd@VM-24-2-ubuntu:~$ docker exec -it milvus-standalone netstat -tlnp | grep 19530
rxd@VM-24-2-ubuntu:~$
