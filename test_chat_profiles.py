#!/usr/bin/env python3
"""
测试chat profiles功能的简化版本
"""
import os
import sys
import random
from typing import Optional

import chainlit as cl
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('ai_rag_system/.env')

# 模拟list_collections函数
def list_collections():
    """模拟获取知识库列表"""
    try:
        # 这里可以返回一些测试数据
        return ["技术文档", "产品手册", "FAQ"]
    except Exception as e:
        print(f"获取知识库列表失败: {e}")
        return []

@cl.set_chat_profiles
async def chat_profile(current_user: cl.User):
    # 调试信息
    print(f"=== Chat Profiles 调试信息 ===")
    print(f"当前用户: {current_user}")
    if current_user:
        print(f"用户标识符: {current_user.identifier}")
        print(f"用户元数据: {current_user.metadata}")
    else:
        print("当前用户为None")
    
    # 获取知识库列表
    try:
        kb_list = list_collections()
        print(f"获取到的知识库列表: {kb_list}")
    except Exception as e:
        print(f"获取知识库列表失败: {e}")
        kb_list = []
    
    # 创建基础profiles
    profiles = [
        cl.ChatProfile(
            name="default",
            markdown_description="🤖 **大模型对话**\n\n与AI助手进行自由对话",
            icon="/public/kbs/4.png",
        )
    ]
    
    # 根据用户角色添加不同的profiles
    if current_user and current_user.metadata.get("role") == "admin":
        print("检测到admin用户，添加所有知识库")
        for kb_name in kb_list:
            profiles.append(
                cl.ChatProfile(
                    name=kb_name,
                    markdown_description=f"📚 **{kb_name} 知识库**\n\n基于{kb_name}的智能问答",
                    icon=f"/public/kbs/{random.randint(1, 3)}.jpg",
                )
            )
    else:
        print("非admin用户或未登录用户，添加演示知识库")
        profiles.append(
            cl.ChatProfile(
                name="demo",
                markdown_description="📖 **演示知识库**\n\n体验知识库问答功能",
                icon="/public/kbs/1.jpg",
            )
        )
    
    print(f"返回的profiles数量: {len(profiles)}")
    for i, profile in enumerate(profiles):
        print(f"Profile {i+1}: {profile.name} - {profile.markdown_description}")
    
    return profiles

@cl.set_starters
async def set_starters():
    return [
        cl.Starter(
            label="测试对话",
            message="你好，这是一个测试消息。",
            icon="/public/apidog.svg",
        ),
        cl.Starter(
            label="功能介绍",
            message="请介绍一下这个系统的主要功能。",
            icon="/public/gleam.svg",
        )
    ]

@cl.password_auth_callback
def auth_callback(username: str, password: str) -> Optional[cl.User]:
    print(f"=== 认证回调 ===")
    print(f"用户名: {username}")
    print(f"密码: {'*' * len(password)}")
    
    # 简单的认证逻辑
    if (username, password) == ("admin", "admin"):
        user = cl.User(
            identifier="admin",
            metadata={"role": "admin", "provider": "credentials"}
        )
        print(f"认证成功，创建用户: {user.identifier}, 元数据: {user.metadata}")
        return user
    elif (username, password) == ("user", "user"):
        user = cl.User(
            identifier="user", 
            metadata={"role": "user", "provider": "credentials"}
        )
        print(f"认证成功，创建用户: {user.identifier}, 元数据: {user.metadata}")
        return user
    else:
        print("认证失败")
        return None

@cl.on_chat_start
async def start():
    print("=== 聊天开始 ===")
    current_user = cl.user_session.get("user")
    print(f"聊天开始时的用户: {current_user}")
    
    await cl.Message(
        content="👋 欢迎使用AI RAG系统！\n\n请选择一个聊天模式开始对话。"
    ).send()

@cl.on_message
async def main(message: cl.Message):
    print(f"=== 收到消息 ===")
    print(f"消息内容: {message.content}")
    
    current_user = cl.user_session.get("user")
    chat_profile = cl.user_session.get("chat_profile")
    
    print(f"当前用户: {current_user}")
    print(f"当前聊天模式: {chat_profile}")
    
    # 简单的回复逻辑
    if chat_profile == "default":
        response = f"🤖 [大模型对话模式] 您说: {message.content}"
    elif chat_profile in ["技术文档", "产品手册", "FAQ"]:
        response = f"📚 [知识库模式: {chat_profile}] 正在搜索相关信息...\n\n针对您的问题「{message.content}」，我找到了以下信息..."
    elif chat_profile == "demo":
        response = f"📖 [演示模式] 这是一个演示回复: {message.content}"
    else:
        response = f"收到您的消息: {message.content}"
    
    await cl.Message(content=response).send()

if __name__ == "__main__":
    print("启动测试版UI...")
    print("请访问 http://localhost:8000")
    print("测试账号:")
    print("- admin/admin (管理员)")
    print("- user/user (普通用户)")
